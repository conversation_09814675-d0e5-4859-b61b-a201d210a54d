{"info": {"description": "The API of the Aerosint recoater. It allows to control the various parameters of the printing head.", "title": "Aerosint API", "version": "3.1.1"}, "openapi": "3.0.3", "paths": {"/config": {"get": {"description": "Returns the configuration variables of the recoater.", "operationId": "get_config", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1config/put/requestBody/content/application~1json/schema"}}}, "description": "<PERSON><PERSON><PERSON> config successfully retrieved."}}, "summary": "Get the recoater configuration variables.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the configuration variables of the recoater.", "operationId": "put_config", "requestBody": {"content": {"application/json": {"schema": {"properties": {"build_space_diameter": {"description": "The diameter of the build space [mm].", "example": 250, "format": "double", "minimum": 0, "type": "number"}, "build_space_dimensions": {"properties": {"length": {"description": "The length of the build space [mm].", "example": 250, "format": "double", "minimum": 0, "type": "number"}, "width": {"description": "The width of the build space [mm].", "example": 96, "format": "double", "minimum": 0, "type": "number"}}, "required": ["length", "width"], "type": "object"}, "ejection_matrix_size": {"description": "The number of points in the ejection matrix.", "example": 192, "format": "int32", "minimum": 0, "type": "integer"}, "gaps": {"description": "The list of gaps between the drums.", "items": {"description": "The distance between two consecutive drums [mm].", "example": 130, "format": "double", "minimum": 0, "type": "number"}, "type": "array"}, "resolution": {"description": "The resolution of the recoater, the size of one pixel [µm].", "example": 500, "format": "int32", "minimum": 0, "type": "integer"}}, "type": "object"}}}, "required": true}, "responses": {"204": {"description": "<PERSON><PERSON><PERSON> config successfully set."}, "400": {"content": {"application/json": {"schema": {"description": "HTTP status code and error message returned by the server.", "properties": {"message": {"example": "Error message", "type": "string"}, "status_code": {"example": 200, "format": "int32", "type": "integer"}}, "required": ["status_code", "message"], "type": "object"}}}, "description": "Bad request"}}, "summary": "Set the recoater configuration variables.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/debug/logs": {"get": {"description": "Returns the content of the log file paginated with the given parameters.", "operationId": "get_logs", "parameters": [{"description": "The number of logged lines to skip.", "in": "query", "name": "offset", "schema": {"default": 0, "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "The number of logged lines to return.", "in": "query", "name": "limit", "schema": {"default": 20, "format": "int32", "maximum": 50, "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"text/plain": {"schema": {"format": "binary", "type": "string"}}}, "description": "Logs successfully retrieved."}}, "summary": "Get parts of the log file.", "tags": ["Debug"]}}, "/debug/version": {"get": {"description": "Returns the version numbers of the server and the API.", "operationId": "get_versions", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"api": {"description": "The version number.", "example": "3.0.0", "type": "string"}, "server": {"description": "The version number.", "example": "3.1.0", "type": "string"}}, "required": ["server", "api"], "type": "object"}}}, "description": "Version numbers successfully retrieved."}}, "summary": "Get the software version numbers.", "tags": ["Debug"]}, "post": {"description": "Sends an executable that should replace the current application.", "operationId": "set_update", "requestBody": {"content": {"application/octet-stream": {"schema": {"format": "binary", "type": "string"}}}, "required": true}, "responses": {"204": {"description": "Update file successfully posted."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}}, "summary": "Post a software update.", "tags": ["Debug"]}}, "/drums": {"get": {"description": "Returns information about the drums.", "operationId": "get_drums_info", "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/200/content/application~1json/schema"}, "type": "array"}}}, "description": "Drums info successfully retrieved."}}, "summary": "Get drums info.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}": {"get": {"description": "Returns information about the specified drum.", "operationId": "get_drum_info", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"circumference": {"description": "The drum's circumference [mm].", "example": 329.867, "format": "double", "minimum": 0, "type": "number"}, "id": {"format": "int32", "minimum": 0, "type": "integer"}, "position": {"description": "The drum's position [mm].", "example": 12.345, "format": "double", "minimum": 0, "type": "number"}, "running": {"description": "A flag that indicates if the drum moves.", "type": "boolean"}}, "required": ["id", "circumference", "running", "position"], "type": "object"}}}, "description": "Drum info successfully retrieved."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1config/put/responses/400/content/application~1json/schema"}}}, "description": "Not found"}}, "summary": "Get drum info.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/blade/screws": {"get": {"description": "Returns information about the two screws of the specified drum's scraping blade.", "operationId": "get_blade_screws_info", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1blade~1screws~1%7Bscrew_id%7D/get/responses/200/content/application~1json/schema"}, "type": "array"}}}, "description": "Drum blade screws info successfully retrieved."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get info on the scraping blade screws.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/blade/screws/motion": {"delete": {"description": "Cancels and removes the current motion command.", "operationId": "cancel_blade_screws_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"204": {"description": "Motion command created."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Delete a motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}, "get": {"description": "Returns the current motion command if there is a motion. Returns nothing otherwise.", "operationId": "get_blade_screws_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1blade~1screws~1motion/post/requestBody/content/application~1json/schema"}}}, "description": "Motion command successfully retrieved."}, "204": {"description": "There is currently no motion."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get the current motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}, "post": {"description": "Creates a motion command if possible.", "operationId": "set_blade_screws_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"distance": {"description": "The absolute or relative distance of the motion [µm].", "example": 2345, "format": "double", "type": "number"}, "mode": {"description": "Motion's mode:\n  * absolute - Absolute movement, goes to the specified position.\n  * relative - Relative movement, travels the given distance.\n  * homing - Homing movement, returns to its reference position.\n", "enum": ["absolute", "relative", "homing"], "example": "relative", "type": "string"}}, "required": ["mode"], "type": "object"}}}, "required": true}, "responses": {"201": {"description": "Motion command created."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}, "409": {"$ref": "#/paths/~1print~1job/post/responses/409"}}, "summary": "Post a motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/blade/screws/{screw_id}": {"get": {"description": "Returns information about the specified drum's scraping blade screw.", "operationId": "get_blade_screw_info", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}, {"description": "The drum blade screw's ID.", "in": "path", "name": "screw_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"id": {"format": "int32", "minimum": 0, "type": "integer"}, "position": {"description": "The blade's position [µm].", "example": 12.345, "format": "double", "minimum": 0, "type": "number"}, "running": {"description": "A flag that indicates if the blade moves.", "type": "boolean"}}, "required": ["id", "running", "position"], "type": "object"}}}, "description": "Drum blade screw info successfully retrieved."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get info on a single screw of the specified drum's scraping blade.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/blade/screws/{screw_id}/motion": {"delete": {"description": "Cancels and removes the current motion command.", "operationId": "cancel_blade_screw_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}, {"description": "The drum blade screw's ID.", "in": "path", "name": "screw_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"204": {"description": "Motion command created."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Delete a motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}, "get": {"description": "Returns the current motion command if there is a motion. Returns nothing otherwise.", "operationId": "get_blade_screw_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}, {"description": "The drum blade screw's ID.", "in": "path", "name": "screw_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1blade~1screws~1%7Bscrew_id%7D~1motion/post/requestBody/content/application~1json/schema"}}}, "description": "Motion command successfully retrieved."}, "204": {"description": "There is currently no motion."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get the current motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}, "post": {"description": "Creates a motion command if possible.", "operationId": "set_blade_screw_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}, {"description": "The drum blade screw's ID.", "in": "path", "name": "screw_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"distance": {"description": "The relative distance of the motion [µm].", "example": 2345, "format": "double", "type": "number"}}, "required": ["distance"], "type": "object"}}}, "required": true}, "responses": {"201": {"description": "Motion command created."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}, "409": {"$ref": "#/paths/~1print~1job/post/responses/409"}}, "summary": "Post a motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/config": {"get": {"description": "Returns the configuration of the specified drum.", "operationId": "get_drum_config", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1config/put/requestBody/content/application~1json/schema"}}}, "description": "Drum config successfully retrieved."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get drum config.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the configuration of the specified drum.", "operationId": "put_drum_config", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"powder_offset": {"description": "The drum's powder offset [pixel].", "example": 2, "format": "int32", "type": "integer"}, "theta_offset": {"description": "The drum's theta offset [mm].", "example": 12.3, "format": "double", "minimum": 0, "type": "number"}}, "required": ["theta_offset"], "type": "object"}}}, "required": true}, "responses": {"204": {"description": "Drum config successfully set."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Set drum config.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/ejection": {"get": {"description": "Returns the maximum, target and current value of the specified drum's ejection pressure. It uses the given units (default Pascal)", "operationId": "get_drum_ejection", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}, {"description": "The pressure unit to use", "in": "query", "name": "unit", "schema": {"description": "Units:\n  * Pascal\n  * bar\n", "enum": ["pascal", "bar"], "example": "pascal", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"maximum": {"description": "The maximum ejection pressure of the drum.", "example": 300, "format": "double", "minimum": 0, "type": "number"}, "target": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1ejection/put/requestBody/content/application~1json/schema/properties/target"}, "unit": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1ejection/get/parameters/1/schema"}, "value": {"description": "The current ejection pressure of the drum.", "example": 100, "format": "double", "minimum": 0, "type": "number"}}, "type": "object"}}}, "description": "Drum ejection successfully retrieved."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get drum ejection pressure info.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the target ejection pressure that the specified drum has to reach. Default unit is Pascal.", "operationId": "put_drum_ejection", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"target": {"description": "The target ejection pressure of the drum.", "example": 200, "format": "double", "minimum": 0, "type": "number"}, "unit": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1ejection/get/parameters/1/schema"}}, "required": ["target", "unit"], "type": "object"}}}, "required": true}, "responses": {"204": {"description": "Drum ejection pressure successfully set."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Set drum ejection pressure.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/geometry": {"delete": {"description": "Removes the current geometry of the specified drum.", "operationId": "delete_drum_geometry", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"204": {"description": "Drum geometry successfully deleted."}}, "summary": "Delete the drum geometry.", "tags": ["<PERSON><PERSON><PERSON>"]}, "get": {"description": "Returns the current geometry of the specified drum. It returns a PNG image representation of the geometry.", "operationId": "get_drum_geometry", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"image/png": {"schema": {"format": "binary", "type": "string"}}}, "description": "PNG image successfully retrieved."}}, "summary": "Get the drum geometry.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the geometry of the specified drum. The geometry can either be a PNG file or a CLI file.", "operationId": "put_drum_geometry", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/octet-stream": {"schema": {"format": "binary", "type": "string"}}}, "required": true}, "responses": {"204": {"description": "Drum geometry successfully set."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}}, "summary": "Set the drum geometry.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/motion": {"delete": {"description": "Cancels and removes the current motion command.", "operationId": "cancel_drum_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"204": {"description": "Motion command cancelled."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Delete the current motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}, "get": {"description": "Returns the current motion command if there is a motion. Returns nothing otherwise.", "operationId": "get_drum_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1motion/post/requestBody/content/application~1json/schema"}}}, "description": "Motion command successfully retrieved."}, "204": {"description": "There is currently no motion."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get the current motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}, "post": {"description": "Creates a motion command if possible.", "operationId": "set_drum_motion", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"distance": {"description": "The absolute or relative distance of the motion [mm].", "example": 100, "format": "double", "type": "number"}, "mode": {"description": "Motion's mode:\n  * absolute - Absolute movement, goes to the specified position.\n  * relative - Relative movement, travels the given distance.\n  * turns - Relative movement, travels the given number of turns.\n  * speed - Infinite movement at the given speed.\n  * homing - Homing movement, returns to its reference position.\n", "enum": ["absolute", "relative", "turns", "speed", "homing"], "example": "relative", "type": "string"}, "speed": {"description": "The speed of the motion [mm/s].", "example": 30, "format": "double", "minimum": 0, "type": "number"}, "turns": {"description": "The number of turns of the motion.", "example": 2, "format": "double", "type": "number"}}, "required": ["mode", "speed"], "type": "object"}}}, "required": true}, "responses": {"201": {"description": "Motion command created."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}, "409": {"$ref": "#/paths/~1print~1job/post/responses/409"}}, "summary": "Post a motion command.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/drums/{drum_id}/suction": {"get": {"description": "Returns the maximum, target and current value of the specified drum's suction pressure.", "operationId": "get_drum_suction", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"maximum": {"description": "The maximum suction pressure of the drum [Pa].", "example": 2.34, "format": "double", "minimum": 0, "type": "number"}, "target": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D~1suction/put/requestBody/content/application~1json/schema/properties/target"}, "value": {"description": "The current suction pressure of the drum [Pa].", "example": 1.23, "format": "double", "minimum": 0, "type": "number"}}, "type": "object"}}}, "description": "Drum suction successfully retrieved."}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Get drum suction pressure info.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the target suction pressure that the specified drum has to reach.", "operationId": "put_drum_suction", "parameters": [{"description": "The drum's ID.", "in": "path", "name": "drum_id", "required": true, "schema": {"format": "int32", "minimum": 0, "type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"target": {"description": "The target suction pressure of the drum [Pa].", "example": 1.45, "format": "double", "minimum": 0, "type": "number"}}, "required": ["target"], "type": "object"}}}, "required": true}, "responses": {"204": {"description": "Drum suction pressure successfully set."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}, "404": {"$ref": "#/paths/~1drums~1%7Bdrum_id%7D/get/responses/404"}}, "summary": "Set drum suction pressure target.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/layer/parameters": {"get": {"description": "Returns the current parameters of the layer.", "operationId": "get_layer_parameters", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"filling_id": {"$ref": "#/paths/~1layer~1parameters/put/requestBody/content/application~1json/schema/properties/filling_id"}, "max_x_offset": {"description": "The maximum offset along the X axis [mm].", "example": 100, "format": "double", "minimum": 0, "type": "number"}, "powder_saving": {"$ref": "#/paths/~1layer~1parameters/put/requestBody/content/application~1json/schema/properties/powder_saving"}, "speed": {"$ref": "#/paths/~1layer~1parameters/put/requestBody/content/application~1json/schema/properties/speed"}, "x_offset": {"$ref": "#/paths/~1layer~1parameters/put/requestBody/content/application~1json/schema/properties/x_offset"}}, "required": ["filling_id", "speed"], "type": "object"}}}, "description": "Layer parameters successfully retrieved."}}, "summary": "Get layer's parameters.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the parameters of the current layer.", "operationId": "put_layer_parameters", "requestBody": {"content": {"application/json": {"schema": {"properties": {"filling_id": {"description": "The ID of the drum with the filling material powder. If there should not be any filling, set this index to -1.", "example": 1, "format": "int32", "type": "integer"}, "powder_saving": {"default": true, "description": "A flag indicating if the powder saving strategies are used or not.", "type": "boolean"}, "speed": {"description": "The patterning speed [mm/s].", "example": 30, "format": "double", "minimum": 0, "type": "number"}, "x_offset": {"description": "The offset along the X axis [mm].", "example": 0, "format": "double", "minimum": 0, "type": "number"}}, "required": ["filling_id", "speed"], "type": "object"}}}, "required": true}, "responses": {"204": {"description": "Layer parameters successfully set."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}}, "summary": "Set layer's parameters.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/layer/preview": {"get": {"description": "Returns a PNG image preview of the layer. The image is a representation of the powder allocation.", "operationId": "get_layer_preview", "responses": {"200": {"content": {"image/png": {"schema": {"format": "binary", "type": "string"}}}, "description": "Layer preview retrieved."}}, "summary": "Get layer preview.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/leveler/pressure": {"get": {"description": "Returns the maximum, target and current value of the leveler pressure.", "operationId": "get_leveler_pressure", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"maximum": {"description": "The maximum pressure of the leveler [Pa].", "example": 2.34, "format": "double", "minimum": 0, "type": "number"}, "target": {"$ref": "#/paths/~1leveler~1pressure/put/requestBody/content/application~1json/schema/properties/target"}, "value": {"description": "The current pressure of the leveler [Pa].", "example": 1.23, "format": "double", "minimum": 0, "type": "number"}}, "type": "object"}}}, "description": "Leveler pressure successfully retrieved."}}, "summary": "Get leveler pressure info.", "tags": ["<PERSON><PERSON><PERSON>"]}, "put": {"description": "Defines the target pressure that the leveler has to reach.", "operationId": "put_leveler_pressure", "requestBody": {"content": {"application/json": {"schema": {"properties": {"target": {"description": "The target pressure of the leveler [Pa].", "example": 1.45, "format": "double", "minimum": 0, "type": "number"}}, "required": ["target"], "type": "object"}}}, "required": true}, "responses": {"204": {"description": "Leveler pressure successfully set."}, "400": {"$ref": "#/paths/~1config/put/responses/400"}}, "summary": "Set leveler pressure target.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/leveler/sensor": {"get": {"description": "Returns the current state of the magnetic sensor on the leveler.", "operationId": "get_leveler_sensor_state", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"state": {"description": "The state of the leveler sensor.", "type": "boolean"}}, "type": "object"}}}, "description": "Leveler sensor state successfully retrieved."}}, "summary": "Get leveler sensor state.", "tags": ["<PERSON><PERSON><PERSON>"]}}, "/print/job": {"delete": {"description": "Cancels and removes the current printing job.", "operationId": "cancel_print_job", "responses": {"204": {"description": "Print job successfully cancelled."}}, "summary": "Delete the current print job."}, "post": {"description": "Creates a printing job if the server is ready to start. The recoater starts the printing procedure and waits for the synchro signal.", "operationId": "start_print_job", "responses": {"202": {"description": "Print job successfully created."}, "409": {"content": {"application/json": {"schema": {"$ref": "#/paths/~1config/put/responses/400/content/application~1json/schema"}}}, "description": "Conflict"}}, "summary": "Post a print job."}}, "/state": {"get": {"description": "Returns the current state of the recoater.", "operationId": "get_state", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"state": {"description": "Recoater's state:\n  * ready - The recoater is waiting requests.\n  * printing - The recoater is printing a layer.\n  * error - The recoater has errors.\n", "enum": ["ready", "printing", "error"], "example": "ready", "type": "string"}}, "required": ["state"], "type": "object"}}}, "description": "Recoater state successfully retrieved."}}, "summary": "Get the recoater's state.", "tags": ["<PERSON><PERSON><PERSON>"]}, "post": {"description": "Changes the recoater state. Allows to restart or shutdown the recoater server.", "operationId": "set_state", "parameters": [{"description": "The state change action to perform.", "in": "query", "name": "action", "required": true, "schema": {"enum": ["restart", "shutdown"], "example": "restart", "type": "string"}}], "responses": {"202": {"description": "Command accepted. State change pending."}}, "summary": "Set the recoater state.", "tags": ["<PERSON><PERSON><PERSON>"]}}}, "servers": [{"url": "/api/v3"}], "tags": [{"description": "Endpoints used for maintenance and debugging.", "name": "Debug"}, {"description": "Endpoints used to control the recoater.", "name": "<PERSON><PERSON><PERSON>"}]}