"""
Recoater Client Service
======================

This module provides the RecoaterClient class that handles all communication
with the Aerosint SPD Recoater hardware API. It serves as the single point
of contact for hardware operations.

The client implements methods for all endpoints defined in the openapi.json
specification and provides proper error handling and type safety.
"""

import requests
import time
from typing import Dict, Any, Optional
import logging
from requests.exceptions import RequestException, ConnectionError, Timeout

logger = logging.getLogger(__name__)


class RecoaterConnectionError(Exception):
    """Raised when connection to recoater hardware fails."""
    pass


class RecoaterAPIError(Exception):
    """Raised when recoater API returns an error response."""
    pass


class RecoaterClient:
    """
    Client for communicating with the Aerosint SPD Recoater hardware API.
    
    This class implements all the endpoints defined in the openapi.json
    specification and provides a clean interface for the backend to
    interact with the recoater hardware.
    """
    
    def __init__(self, base_url: str, timeout: float = 5.0):
        """
        Initialize the RecoaterClient.
        
        Args:
            base_url: The base URL of the recoater API (e.g., "http://*************:8080")
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        logger.info(f"RecoaterClient initialized with base_url: {self.base_url}")
    
    def _make_request(self, method: str, endpoint: str, return_raw: bool = False, **kwargs):
        """
        Make a request to the recoater API with proper error handling and retry logic.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without leading slash)
            return_raw: If True, return raw response object instead of JSON
            **kwargs: Additional arguments for requests

        Returns:
            JSON response as dictionary or raw response object if return_raw=True

        Raises:
            RecoaterConnectionError: If connection fails after all retries
            RecoaterAPIError: If API returns error status
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        max_attempts = 3
        retry_delay = 0.3  # 300ms

        for attempt in range(max_attempts):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1}/{max_attempts})")
                response = self.session.request(
                    method=method,
                    url=url,
                    timeout=self.timeout,
                    **kwargs
                )

                # Check for HTTP errors
                if response.status_code >= 400:
                    logger.error(f"API error {response.status_code}: {response.text}")
                    raise RecoaterAPIError(f"API returned status {response.status_code}: {response.text}")

                # Return raw response if requested (for binary data like images)
                if return_raw:
                    return response

                # Try to parse JSON response
                try:
                    return response.json()
                except ValueError:
                    # If response is not JSON, return empty dict
                    return {}

            except (ConnectionError, Timeout) as e:
                if attempt < max_attempts - 1:
                    logger.warning(f"Connection error on attempt {attempt + 1}: {e}. Retrying in {retry_delay}s...")
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"Connection error after {max_attempts} attempts: {e}")
                    raise RecoaterConnectionError(f"Failed to connect to recoater after {max_attempts} attempts: {e}")
            except RequestException as e:
                logger.error(f"Request error: {e}")
                raise RecoaterConnectionError(f"Request failed: {e}")
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get the current state of the recoater.
        
        Returns:
            Dictionary containing the recoater state information
        """
        return self._make_request("GET", "/state")
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get the recoater configuration variables.
        
        Returns:
            Dictionary containing the recoater configuration
        """
        return self._make_request("GET", "/config")
    
    def set_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Set the recoater configuration variables.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Response from the API
        """
        return self._make_request("PUT", "/config", json=config)
    
    def get_drums(self) -> Dict[str, Any]:
        """
        Get information about all drums.
        
        Returns:
            Dictionary containing drums information
        """
        return self._make_request("GET", "/drums")
    
    def get_drum(self, drum_id: int) -> Dict[str, Any]:
        """
        Get information about a specific drum.
        
        Args:
            drum_id: The drum's ID
            
        Returns:
            Dictionary containing drum information
        """
        return self._make_request("GET", f"/drums/{drum_id}")
    
    def health_check(self) -> bool:
        """
        Perform a simple health check to verify connectivity.

        Returns:
            True if recoater is reachable and responding, False otherwise
        """
        try:
            self.get_state()
            return True
        except (RecoaterConnectionError, RecoaterAPIError):
            return False

    # NOTE: Axis control methods have been removed as they are not supported by the actual hardware API.
    # The openapi.json specification does not include any axis-related endpoints.
    # These methods were causing failures when used against real hardware.
    # The MockRecoaterClient still supports axis operations for development/testing purposes.

    # Drum Control Methods
    # These methods implement the drum control endpoints from the openapi.json specification

    def get_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing current motion information
        """
        return self._make_request("GET", f"/drums/{drum_id}/motion")

    def set_drum_motion(self, drum_id: int, mode: str, speed: float, distance: float = None, turns: float = None) -> Dict[str, Any]:
        """
        Create a motion command for a drum.

        Args:
            drum_id: The drum's ID
            mode: Motion mode ('absolute', 'relative', 'turns', 'speed', 'homing')
            speed: The speed of the motion [mm/s]
            distance: The distance of the motion [mm] (for absolute/relative modes)
            turns: The number of turns (for turns mode)

        Returns:
            Response from the API
        """
        payload = {
            "mode": mode,
            "speed": speed
        }

        if distance is not None:
            payload["distance"] = distance
        if turns is not None:
            payload["turns"] = turns

        return self._make_request("POST", f"/drums/{drum_id}/motion", json=payload)

    def cancel_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/motion")

    def get_drum_ejection(self, drum_id: int, unit: str = "pascal") -> Dict[str, Any]:
        """
        Get the ejection pressure information for a drum.

        Args:
            drum_id: The drum's ID
            unit: Pressure unit ('pascal' or 'bar')

        Returns:
            Dictionary containing ejection pressure information
        """
        params = {"unit": unit}
        return self._make_request("GET", f"/drums/{drum_id}/ejection", params=params)

    def set_drum_ejection(self, drum_id: int, target: float, unit: str = "pascal") -> Dict[str, Any]:
        """
        Set the target ejection pressure for a drum.

        Args:
            drum_id: The drum's ID
            target: Target ejection pressure
            unit: Pressure unit ('pascal' or 'bar')

        Returns:
            Response from the API
        """
        payload = {
            "target": target,
            "unit": unit
        }
        return self._make_request("PUT", f"/drums/{drum_id}/ejection", json=payload)

    def get_drum_suction(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the suction pressure information for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing suction pressure information
        """
        return self._make_request("GET", f"/drums/{drum_id}/suction")

    def set_drum_suction(self, drum_id: int, target: float) -> Dict[str, Any]:
        """
        Set the target suction pressure for a drum.

        Args:
            drum_id: The drum's ID
            target: Target suction pressure [Pa]

        Returns:
            Response from the API
        """
        payload = {"target": target}
        return self._make_request("PUT", f"/drums/{drum_id}/suction", json=payload)

    # Blade Control Methods
    # These methods implement the blade control endpoints from the openapi.json specification

    def get_blade_screws_info(self, drum_id: int) -> Dict[str, Any]:
        """
        Get information about the two screws of the specified drum's scraping blade.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing blade screws information
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws")

    def get_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for the blade screws.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing current motion information, or empty if no motion
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws/motion")

    def set_blade_screws_motion(self, drum_id: int, mode: str, distance: float = None) -> Dict[str, Any]:
        """
        Create a motion command for the blade screws.

        Args:
            drum_id: The drum's ID
            mode: Motion mode ('absolute', 'relative', 'homing')
            distance: The distance of the motion [µm] (for absolute/relative modes)

        Returns:
            Response from the API
        """
        payload = {
            "mode": mode
        }

        if distance is not None:
            payload["distance"] = distance

        return self._make_request("POST", f"/drums/{drum_id}/blade/screws/motion", json=payload)

    def cancel_blade_screws_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for the blade screws.

        Args:
            drum_id: The drum's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/blade/screws/motion")

    def get_blade_screw_info(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Get information about a specific screw of the drum's scraping blade.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Dictionary containing blade screw information
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws/{screw_id}")

    def get_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for a specific blade screw.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Dictionary containing current motion information, or empty if no motion
        """
        return self._make_request("GET", f"/drums/{drum_id}/blade/screws/{screw_id}/motion")

    def set_blade_screw_motion(self, drum_id: int, screw_id: int, distance: float) -> Dict[str, Any]:
        """
        Create a motion command for a specific blade screw.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID
            distance: The relative distance of the motion [µm]

        Returns:
            Response from the API
        """
        payload = {
            "distance": distance
        }
        return self._make_request("POST", f"/drums/{drum_id}/blade/screws/{screw_id}/motion", json=payload)

    def cancel_blade_screw_motion(self, drum_id: int, screw_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for a specific blade screw.

        Args:
            drum_id: The drum's ID
            screw_id: The screw's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/blade/screws/{screw_id}/motion")

    # Leveler Control Methods
    # These methods implement the leveler control endpoints from the openapi.json specification

    def get_leveler_pressure(self) -> Dict[str, Any]:
        """
        Get the leveler pressure information.

        Returns:
            Dictionary containing leveler pressure information (maximum, target, value)
        """
        return self._make_request("GET", "/leveler/pressure")

    def set_leveler_pressure(self, target: float) -> Dict[str, Any]:
        """
        Set the target pressure for the leveler.

        Args:
            target: Target leveler pressure [Pa]

        Returns:
            Response from the API
        """
        payload = {"target": target}
        return self._make_request("PUT", "/leveler/pressure", json=payload)

    def get_leveler_sensor(self) -> Dict[str, Any]:
        """
        Get the current state of the magnetic sensor on the leveler.

        Returns:
            Dictionary containing leveler sensor state
        """
        return self._make_request("GET", "/leveler/sensor")

    # Print Control Methods
    # These methods implement the print control endpoints from the openapi.json specification

    def get_layer_parameters(self) -> Dict[str, Any]:
        """
        Get the current parameters of the layer.

        Returns:
            Dictionary containing layer parameters (filling_id, speed, powder_saving, x_offset, max_x_offset)
        """
        return self._make_request("GET", "/layer/parameters")

    def set_layer_parameters(self, filling_id: int, speed: float, powder_saving: bool = True, x_offset: float = None) -> Dict[str, Any]:
        """
        Set the parameters of the current layer.

        Args:
            filling_id: The ID of the drum with the filling material powder. Set to -1 for no filling.
            speed: The patterning speed [mm/s]
            powder_saving: Flag indicating if powder saving strategies are used (default: True)
            x_offset: The offset along the X axis [mm] (optional)

        Returns:
            Response from the API
        """
        payload = {
            "filling_id": filling_id,
            "speed": speed,
            "powder_saving": powder_saving
        }
        if x_offset is not None:
            payload["x_offset"] = x_offset

        return self._make_request("PUT", "/layer/parameters", json=payload)

    def get_layer_preview(self) -> bytes:
        """
        Get layer preview as PNG image.

        Returns:
            PNG image data as bytes
        """
        response = self._make_request("GET", "/layer/preview", return_raw=True)
        return response.content

    def start_print_job(self) -> Dict[str, Any]:
        """
        Create a printing job if the server is ready to start.

        Returns:
            Response from the API
        """
        return self._make_request("POST", "/print/job")

    def cancel_print_job(self) -> Dict[str, Any]:
        """
        Cancel and remove the current printing job.

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", "/print/job")

    # File Management Methods
    # These methods implement the drum geometry file management endpoints from the openapi.json specification

    def upload_drum_geometry(self, drum_id: int, file_data: bytes, content_type: str = "application/octet-stream") -> Dict[str, Any]:
        """
        Upload geometry file (PNG or CLI) to a specific drum.

        Args:
            drum_id: The ID of the drum to upload geometry to
            file_data: The binary file data to upload
            content_type: The content type of the file (default: application/octet-stream)

        Returns:
            Response from the API
        """
        headers = {"Content-Type": content_type}
        return self._make_request("PUT", f"/drums/{drum_id}/geometry", data=file_data, headers=headers)

    def download_drum_geometry(self, drum_id: int) -> bytes:
        """
        Download geometry file from a specific drum as PNG image.

        Args:
            drum_id: The ID of the drum to download geometry from

        Returns:
            PNG image data as bytes
        """
        response = self._make_request("GET", f"/drums/{drum_id}/geometry", return_raw=True)
        return response.content

    def delete_drum_geometry(self, drum_id: int) -> Dict[str, Any]:
        """
        Delete geometry file from a specific drum.

        Args:
            drum_id: The ID of the drum to delete geometry from

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/geometry")

    def get_print_job_status(self) -> Dict[str, Any]:
        """
        Get the current print job status by checking the recoater state.

        Returns:
            Dictionary containing print job status information
        """
        state_response = self.get_state()
        return {
            "state": state_response.get("state", "unknown"),
            "is_printing": state_response.get("state") == "printing",
            "has_error": state_response.get("state") == "error"
        }
